import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  StatusBar,
  Dimensions,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Video from 'react-native-video';
import { 
  AppButton, 
  Winicon, 
  showSnackbar, 
  ComponentStatus 
} from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import { SavedVideo } from '../../../utils/VideoDownloadManager';
import OfflineVideoStorage from '../../../utils/OfflineVideoStorage';
import { useRoute, RouteProp } from '@react-navigation/native';
import { navigateBack } from '../../../router/router';
import RNFS from 'react-native-fs';

interface OfflineVideoPlayerRouteParams {
  video: SavedVideo;
}

type OfflineVideoPlayerRouteProp = RouteProp<{params: OfflineVideoPlayerRouteParams}, 'params'>;

const OfflineVideoPlayer: React.FC = () => {
  const route = useRoute<OfflineVideoPlayerRouteProp>();
  const { video } = route.params;
  
  const [paused, setPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [fullscreen, setFullscreen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileExists, setFileExists] = useState(false);
  
  const videoRef = useRef<Video>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const offlineStorage = OfflineVideoStorage.getInstance();

  useEffect(() => {
    checkVideoFile();
  }, []);

  useEffect(() => {
    if (showControls) {
      resetControlsTimeout();
    }
  }, [showControls]);

  const checkVideoFile = async () => {
    try {
      const exists = await RNFS.exists(video.localPath);
      setFileExists(exists);
      
      if (!exists) {
        setError('File video không tồn tại');
        Alert.alert(
          'Lỗi',
          'File video không tồn tại. Video có thể đã bị xóa.',
          [
            {
              text: 'Quay lại',
              onPress: () => navigateBack(),
            },
          ]
        );
      }
    } catch (err) {
      console.error('Error checking video file:', err);
      setError('Không thể kiểm tra file video');
    }
  };

  const resetControlsTimeout = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  const handleVideoPress = () => {
    setShowControls(true);
    resetControlsTimeout();
  };

  const handlePlayPause = () => {
    setPaused(!paused);
    setShowControls(true);
    resetControlsTimeout();
  };

  const handleSeek = (time: number) => {
    if (videoRef.current) {
      videoRef.current.seek(time);
      setCurrentTime(time);
    }
  };

  const handleLoad = (data: any) => {
    setDuration(data.duration);
    setLoading(false);
    console.log('Video loaded:', data);
  };

  const handleProgress = (data: any) => {
    setCurrentTime(data.currentTime);
  };

  const handleError = (error: any) => {
    console.error('Video error:', error);
    setError('Không thể phát video');
    setLoading(false);
    showSnackbar({
      message: 'Có lỗi xảy ra khi phát video',
      status: ComponentStatus.ERROR,
    });
  };

  const handleEnd = () => {
    setPaused(true);
    setCurrentTime(0);
    if (videoRef.current) {
      videoRef.current.seek(0);
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleDeleteVideo = () => {
    Alert.alert(
      'Xóa video',
      `Bạn có chắc chắn muốn xóa video "${video.videoName}" không?`,
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xóa',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await offlineStorage.deleteVideo(video.id);
              if (success) {
                showSnackbar({
                  message: 'Đã xóa video thành công',
                  status: ComponentStatus.SUCCESS,
                });
                navigateBack();
              } else {
                showSnackbar({
                  message: 'Không thể xóa video',
                  status: ComponentStatus.ERROR,
                });
              }
            } catch (error) {
              console.error('Error deleting video:', error);
              showSnackbar({
                message: 'Có lỗi xảy ra khi xóa video',
                status: ComponentStatus.ERROR,
              });
            }
          },
        },
      ],
    );
  };

  const renderControls = () => {
    if (!showControls || loading || error) return null;

    return (
      <View style={styles.controlsContainer}>
        {/* Top controls */}
        <View style={styles.topControls}>
          <AppButton
            prefixIcon={'outline/arrows/left-arrow'}
            prefixIconSize={24}
            backgroundColor={ColorThemes.light.transparent}
            textColor={ColorThemes.light.white}
            borderColor="transparent"
            containerStyle={styles.controlButton}
            onPress={navigateBack}
          />
          
          <View style={styles.videoInfo}>
            <Text style={styles.videoTitle} numberOfLines={1}>
              {video.videoName}
            </Text>
            <Text style={styles.lessonTitle} numberOfLines={1}>
              {video.lessonName}
            </Text>
          </View>
          
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleDeleteVideo}
          >
            <Winicon
              src="outline/user interface/trash"
              size={24}
              color={ColorThemes.light.white}
            />
          </TouchableOpacity>
        </View>

        {/* Center play/pause button */}
        <TouchableOpacity
          style={styles.playPauseButton}
          onPress={handlePlayPause}
        >
          <Winicon
            src={paused ? "fill/media/play" : "fill/media/pause"}
            size={48}
            color={ColorThemes.light.white}
          />
        </TouchableOpacity>

        {/* Bottom controls */}
        <View style={styles.bottomControls}>
          <Text style={styles.timeText}>
            {formatTime(currentTime)}
          </Text>
          
          <View style={styles.progressContainer}>
            <View style={styles.progressTrack}>
              <View 
                style={[
                  styles.progressBar,
                  { width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }
                ]}
              />
            </View>
          </View>
          
          <Text style={styles.timeText}>
            {formatTime(duration)}
          </Text>
        </View>
      </View>
    );
  };

  const renderError = () => {
    return (
      <View style={styles.errorContainer}>
        <Winicon
          src="outline/user interface/alert-triangle"
          size={64}
          color={ColorThemes.light.Error_Color_Main}
        />
        <Text style={styles.errorTitle}>Không thể phát video</Text>
        <Text style={styles.errorText}>{error}</Text>
        
        <AppButton
          title="Quay lại"
          onPress={navigateBack}
          containerStyle={styles.errorButton}
        />
      </View>
    );
  };

  const renderLoading = () => {
    return (
      <View style={styles.loadingContainer}>
        <Winicon
          src="outline/user interface/loading"
          size={48}
          color={ColorThemes.light.Primary_Color_Main}
        />
        <Text style={styles.loadingText}>Đang tải video...</Text>
      </View>
    );
  };

  if (error || !fileExists) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000" />
        <SafeAreaView edges={['top']} style={styles.safeArea} />
        {renderError()}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      <SafeAreaView edges={['top']} style={styles.safeArea} />
      
      <TouchableOpacity 
        style={styles.videoContainer}
        activeOpacity={1}
        onPress={handleVideoPress}
      >
        {loading && renderLoading()}
        
        <Video
          ref={videoRef}
          source={{ uri: `file://${video.localPath}` }}
          style={styles.video}
          paused={paused}
          resizeMode="contain"
          onLoad={handleLoad}
          onProgress={handleProgress}
          onEnd={handleEnd}
          onError={handleError}
          progressUpdateInterval={1000}
        />
        
        {renderControls()}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  safeArea: {
    backgroundColor: '#000',
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  video: {
    flex: 1,
  },
  controlsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  videoInfo: {
    flex: 1,
    marginHorizontal: 16,
  },
  videoTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.white,
  },
  lessonTitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.white,
    opacity: 0.8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  playPauseButton: {
    alignSelf: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  timeText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.white,
    minWidth: 40,
  },
  progressContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  progressTrack: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressBar: {
    height: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderRadius: 2,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  loadingText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.white,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.white,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.white,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 24,
  },
  errorButton: {
    marginTop: 16,
  },
});

export default OfflineVideoPlayer;
