import {Text, View, StyleSheet, Image} from 'react-native';

export const CardTitleGame = ({
  title,
  showIcon,
}: {
  title: string;
  showIcon?: boolean;
}) => {
  return (
    <View style={styles.container}>
      {showIcon && (
        <View style={styles.audioContainer}>
          <Image style={styles.audio} source={require('../assets/audio.png')} />
        </View>
      )}
      <View style={styles.instruction}>
        <Text style={styles.wordText}>{title || ''}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 12,
  },
  instruction: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  wordText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  audioContainer: {
    height: '100%',
    marginRight: 8,
  },
  audio: {
    width: 26,
    height: 26,
  },
});
