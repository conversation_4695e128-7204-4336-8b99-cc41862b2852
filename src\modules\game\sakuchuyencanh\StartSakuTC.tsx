import {
  FlatList,
  Image,
  ImageBackground,
  StyleSheet,
  Text,
  View,
  Vibration,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import CountBadge from '../components/CountQuestions';
import {BottomGame} from '../components/BottomGame';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';
import {CardText} from '../components/CardText';
import {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {PanGestureHandler} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import React from 'react';
import {useGameHook} from '../../../redux/hook/gameHook';
import GameOverModal from '../components/GameOverModel';
import HintModel from '../components/HintModel';
import ModelConfirm from '../components/ModelConfirm';
import ModelPauseGame from '../components/ModelPauseGame';
import {
  checkPositionOrder,
  hasMaxSort,
  replaceObjectById,
} from '../utils/functions';
import {Word} from './models/models';
import ModelDescriptionQuestion from '../components/ModelDescriptionQuestion';
import ModelDoneLevel from '../components/ModelDoneLevel';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useSakuTcHook} from './redux/hooks/sakuTcHook';
import ConfigAPI from '../../../Config/ConfigAPI';
import WinnerModal from './components/WinnerModel';

interface DropZone {
  id: string;
  answer: Word | null;
  index: number;
  showZone?: boolean;
  x: number;
  y: number;
  height: number;
  width: number;
}

// DraggableWord component moved outside to prevent hook issues
const DraggableWord = React.memo(
  ({
    answer,
    onDrop,
    refDropZone,
  }: {
    answer: Word;
    onDrop: (answer: Word) => void;
    refDropZone: React.RefObject<View | null>;
  }) => {
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);
    const scale = useSharedValue(1);
    const zIndex = useSharedValue(0);

    const checkAndHandleDrop = useCallback(
      (eventAbsoluteX: number, eventAbsoluteY: number) => {
        const addSize = 20;
        if (!refDropZone?.current) return;

        refDropZone.current.measureInWindow((x, y, width, height) => {
          const wordX = eventAbsoluteX;
          const wordY = eventAbsoluteY;

          const dropZoneX = x + addSize;
          const dropZoneY = y + addSize;
          const dropZoneRight = x + width + addSize;
          const dropZoneBottom = y + height + addSize;

          if (
            wordX >= dropZoneX &&
            wordX <= dropZoneRight &&
            wordY >= dropZoneY &&
            wordY <= dropZoneBottom
          ) {
            onDrop(answer);
          }
        });
      },
      [onDrop, answer, refDropZone],
    );

    const gestureHandler = useAnimatedGestureHandler({
      onStart: () => {
        'worklet';
        scale.value = withSpring(1.1);
        zIndex.value = 1000;
      },
      onActive: event => {
        'worklet';
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      },
      onEnd: event => {
        'worklet';
        runOnJS(checkAndHandleDrop)(event.absoluteX, event.absoluteY);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        zIndex.value = 0;
      },
    });

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [
        {translateX: translateX.value},
        {translateY: translateY.value},
        {scale: scale.value},
      ],
      zIndex: zIndex.value,
    }));

    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.wordContainer, animatedStyle]}>
          <Text style={styles.wordText}>{answer.text}</Text>
        </Animated.View>
      </PanGestureHandler>
    );
  },
);

const StartSakuTC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const {milestoneId, competenceId} = route.params as {
    milestoneId: string;
    competenceId: string;
  };
  // Hooks
  const sakuTcHook = useSakuTcHook();
  const gameHook = useGameHook();

  // Refs
  const refDropZone = useRef<View>(null);
  const flatListRef = useRef<FlatList>(null);

  // Redux state
  const {
    dataListQuestion,
    listQuestions,
    currentQuestion,
    questionDone,
    totalQuestion,
    currentLevel,
    maxLevel,
    timeLimit,
    bonusLv1,
    bonusLv2,
    bonusLv3,
  } = useSelector((state: RootState) => state.SakuTCStore);
  const {isGameOver, messageGameOver, gem} = useSelector(
    (state: RootState) => state.gameStore,
  );

  // Game state
  const [gameState, setGameState] = useState({
    listZone: [] as DropZone[],
    currentDropZone: null as DropZone | null,
    listAnswer: [] as Word[],
    textAnswer: '',
  });

  // UI state
  const [uiState, setUiState] = useState({
    isShowModelConfirm: false,
    isShowHintModel: false,
    isPauseGame: false,
    isQuestionError: false,
    isWinnerLevel: false,
    isWinnerQuestion: false,
    isWinnerGame: false,
  });

  // Effects
  useEffect(() => {
    initData();
    return () => {
      refDropZone.current = null;
    };
  }, []);

  useEffect(() => {
    if (!currentQuestion?.listWords) return;

    setGameState(prev => ({
      ...prev,
      listAnswer: currentQuestion.listWords,
    }));

    const zones = currentQuestion.listWords.map((item, index) => ({
      id: Math.random().toString(),
      answer: null,
      index,
      x: 0,
      y: 0,
      height: 0,
      width: 0,
    }));

    const updatedZones = findAndUpdateHighestIndexItem(zones);
    setGameState(prev => ({
      ...prev,
      listZone: updatedZones,
    }));

    setTimeout(() => {
      flatListRef.current?.scrollToEnd({animated: true});
    }, 500);
  }, [currentQuestion]);

  useEffect(() => {
    sakuTcHook.startGame();
    gameHook.restartGame();
  }, [dataListQuestion]);

  useEffect(() => {
    gameHook.setData({stateName: 'time', value: timeLimit});
  }, [timeLimit]);

  // Game control functions
  const startGame = useCallback(() => {
    sakuTcHook.startGame();
    gameHook.restartGame();
  }, [sakuTcHook, gameHook]);

  const resetData = () => {
    setUiState(prev => ({...prev, isWinnerLevel: false}));
    setUiState(prev => ({...prev, isWinnerQuestion: false}));
    setUiState(prev => ({...prev, isWinnerGame: false}));
    setUiState(prev => ({...prev, isQuestionError: false}));
    setGameState(prev => ({...prev, listAnswer: []}));
    setGameState(prev => ({...prev, listZone: []}));
    setGameState(prev => ({...prev, textAnswer: ''}));
  };

  const getBonus = () => {
    switch (currentLevel) {
      case 1:
        return bonusLv1;
      case 2:
        return bonusLv2;
      case 3:
        return bonusLv3;
      default:
        return 0;
    }
  };

  const initData = async () => {
    resetData();
    sakuTcHook.loadGameConfig({gameId: ConfigAPI.gameSakuTC});
    sakuTcHook.loadGameQuestions({
      gameId: ConfigAPI.gameSakuTC,
      stage: milestoneId as unknown as number,
      competenceId: competenceId,
    });
    gameHook.getCurrentScore(ConfigAPI.gameSakuTC);
  };

  // game over
  const gameOver = useCallback(
    (message: string) => {
      gameHook.gameOver(message);
    },
    [gameHook],
  );

  // tạm dừng game
  const onPauseGame = useCallback(() => {
    gameHook.pauseGame();
    setUiState(prev => ({...prev, isPauseGame: true}));
  }, [gameHook]);

  // tiếp tục game
  const onContinueGame = useCallback(() => {
    gameHook.continueGame();
    setUiState(prev => ({...prev, isPauseGame: false}));
  }, [gameHook]);

  // hiển thị model confirm
  const onShowModelConfirm = useCallback(() => {
    setUiState(prev => ({...prev, isShowModelConfirm: true}));
  }, []);

  // hiển thị model hint
  const onShowHintModel = useCallback(() => {
    gameHook.setData({stateName: 'gem', value: gem - 10});
    setUiState(prev => ({...prev, isShowHintModel: true}));
  }, [gameHook, gem]);

  // sang câu hỏi tiếp theo
  const nextQuestion = useCallback(() => {
    setUiState(prev => ({...prev, isWinnerQuestion: false}));
    sakuTcHook.nextQuestion();
  }, [sakuTcHook]);

  // thắng level
  const onWinnerLevel = useCallback(() => {
    setUiState(prev => ({...prev, isWinnerLevel: true}));
  }, []);

  // sang level tiếp theo
  const onNextLevel = useCallback(() => {
    setUiState(prev => ({...prev, isWinnerLevel: false}));
    sakuTcHook.nextLevel();
  }, [sakuTcHook]);

  // bắt đầu lại level
  const restartLevel = useCallback(() => {
    setUiState(prev => ({...prev, isQuestionError: false}));
    sakuTcHook.restartLevel();
  }, [sakuTcHook]);

  // Kiểm tra kết quả
  const checkAnswer = useCallback(
    (listZone: DropZone[]) => {
      const answers = listZone.map(zone => zone.answer).reverse();

      if (answers.length !== currentQuestion?.listWords.length) return;

      const isCorrect = checkPositionOrder(answers);
      if (isCorrect) {
        sakuTcHook.setData({
          stateName: 'questionDone',
          value: questionDone + 1,
        });

        if (hasMaxSort(listQuestions, currentQuestion)) {
          if (currentLevel === maxLevel)
            return setUiState(prev => ({...prev, isWinnerGame: true}));
          return onWinnerLevel();
        }

        setUiState(prev => ({...prev, isWinnerQuestion: true}));
      } else {
        // Rung thiết bị khi trả lời sai
        Vibration.vibrate([0, 500, 200, 500]);

        setGameState(prev => ({
          ...prev,
          textAnswer: answers.map(item => item?.text).join(' '),
        }));
        setUiState(prev => ({...prev, isQuestionError: true}));
        sakuTcHook.setData({stateName: 'currentQuestion', value: null});
      }
    },
    [currentQuestion, questionDone, sakuTcHook, onWinnerLevel],
  );

  // tìm và cập nhật item có index cao nhất
  const findAndUpdateHighestIndexItem = useCallback((zones: DropZone[]) => {
    const highestIndexItem = zones
      .filter(zone => zone.answer === null)
      .sort((a, b) => b.index - a.index)[0];

    if (highestIndexItem) {
      setGameState(prev => ({
        ...prev,
        currentDropZone: highestIndexItem,
      }));
      return zones.map(zone => ({
        ...zone,
        showZone: zone.id === highestIndexItem.id,
      }));
    }
    return zones;
  }, []);

  // xoá từ khỏi danh sách
  const removeAnswerFromList = useCallback(
    (answer: Word) => {
      setGameState(prev => {
        const listZoneClone = [...prev.listAnswer];
        const index = listZoneClone.findIndex(z => z.id === answer.id);
        listZoneClone.splice(index, 1);
        return {
          ...prev,
          listAnswer: listZoneClone,
        };
      });
      sakuTcHook.setData({
        stateName: 'currentQuestion',
        value: currentQuestion,
      });
    },
    [currentQuestion, sakuTcHook],
  );

  // thêm từ vào drop zone
  const addWordToDropZone = useCallback(
    (answer: Word) => {
      if (!gameState.currentDropZone) return;

      const updatedZone = {
        ...gameState.currentDropZone,
        answer,
      };

      let updatedZones = replaceObjectById(updatedZone, gameState.listZone);
      updatedZones = findAndUpdateHighestIndexItem(updatedZones);

      setGameState(prev => ({
        ...prev,
        listZone: updatedZones,
      }));

      removeAnswerFromList(answer);

      if (gameState.currentDropZone.index < 1) {
        checkAnswer(updatedZones);
      }
    },
    [
      gameState.currentDropZone,
      findAndUpdateHighestIndexItem,
      removeAnswerFromList,
      checkAnswer,
    ],
  );

  // Memoized list of draggable words
  const memoizedDraggableWords = useMemo(() => {
    return gameState.listAnswer.map((answer: Word) => (
      <DraggableWord
        key={answer.id}
        answer={answer}
        onDrop={addWordToDropZone}
        refDropZone={refDropZone}
      />
    ));
  }, [gameState.listAnswer, addWordToDropZone]);

  // render con chim trên cành cây
  const getBirdBubble = useCallback((side: string) => {
    const text = 'Kéo các từ theo thứ tự tại đây';
    return side === 'left' ? (
      <View style={styles.birdBubbleContainer}>
        <View style={styles.bubbleWrapper}>
          <Image source={require('./assets/bubble_left.png')} />
          <Text style={[styles.bubbleText, styles.bubbleTextLeft]}>{text}</Text>
        </View>
        <View style={styles.birdLeft}>
          <Image source={require('./assets/bird_left.png')} />
        </View>
      </View>
    ) : (
      <View style={styles.birdBubbleRight}>
        <View style={styles.bubbleWrapper}>
          <Image source={require('./assets/bubble_right.png')} />
          <Text style={[styles.bubbleText, styles.bubbleTextRight]}>
            {text}
          </Text>
        </View>
        <View style={styles.birdRight}>
          <Image source={require('./assets/bird_right.png')} />
        </View>
      </View>
    );
  }, []);

  // render cành cây
  const getBranch = useCallback(
    (item: DropZone) => {
      const side = item.index % 2 === 0 ? 'left' : 'right';
      const image =
        side === 'left'
          ? require('./assets/branch_left.png')
          : require('./assets/branch_right.png');

      const isShowBird = item.index > 1 && !item.answer && item.showZone;

      return (
        <View
          style={[
            styles.branchContainer,
            {alignItems: side === 'left' ? 'flex-start' : 'flex-end'},
          ]}>
          <View style={styles.branchWrapper}>
            <ImageBackground source={image} style={styles.branchImage} />
            {isShowBird && getBirdBubble(side)}
            {(item.showZone || item.answer) && (
              <View ref={refDropZone} style={styles.dropZoneContainer}>
                <CardText text={item.answer?.text || '...'} />
              </View>
            )}
          </View>
        </View>
      );
    },
    [getBirdBubble],
  );

  // Render
  return (
    <SafeAreaView style={styles.safeArea}>
      <ImageBackground
        source={require('./assets/background.png')}
        style={styles.backgroundImage}>
        {/* Header */}
        <View style={{marginHorizontal: 16}}>
          <HeadGame
            timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
            isShowSuggest={true}
            onUseHint={onShowModelConfirm}
            gameId={ConfigAPI.gameSakuTC}
          />
          <View>
            <LineProgressBar
              progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
            <View
              style={{flexDirection: 'row', justifyContent: 'space-between'}}>
              <CardText text={`Cấp độ ${currentLevel}`} />
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
          </View>
        </View>

        {/* Body */}
        {uiState.isPauseGame ? (
          <View
            style={{
              zIndex: 1000,
            }}>
            <ModelPauseGame
              visible={uiState.isPauseGame}
              message={'Bạn đang tạm dừng trò chơi'}
              onContinue={onContinueGame}
            />
          </View>
        ) : (
          <>
            <View style={styles.bodyContainer}>
              <FlatList
                ref={flatListRef}
                data={gameState.listZone}
                keyExtractor={item => item.id}
                renderItem={({item}) => getBranch(item)}></FlatList>
            </View>
            <View style={styles.wordsContainer}>{memoizedDraggableWords}</View>
          </>
        )}

        {/* Bottom */}
        <View style={styles.bottomContainer}>
          <BottomGame
            resetGame={startGame}
            backGame={navigation.goBack}
            pauseGame={onPauseGame}
            volumeGame={() => {}}
          />
        </View>
      </ImageBackground>

      {/* Modals */}
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={uiState.isShowModelConfirm}
          closeModal={() =>
            setUiState(prev => ({...prev, isShowModelConfirm: false}))
          }
          onConfirm={onShowHintModel}
          message={`Bạn sẽ bị trừ 10 điểm khi sử dụng trợ giúp này`}
        />
        <HintModel
          isShow={uiState.isShowHintModel}
          closeModal={() =>
            setUiState(prev => ({...prev, isShowHintModel: false}))
          }
          text={currentQuestion?.hint || ''}
        />
        <GameOverModal
          visible={uiState.isQuestionError}
          onClose={() => {}}
          restartGame={restartLevel}
          message={'Tiếc quá sai rồi, làm lại nào'}
          isTimeOut={false}
          isShowCardText={true}
          statusCard={'error'}
          cardText={gameState.textAnswer}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />

        <ModelDescriptionQuestion
          visible={uiState.isWinnerQuestion}
          onNext={nextQuestion}
          message={currentQuestion?.description || ''}
        />
        <ModelDoneLevel
          visible={uiState.isWinnerLevel}
          message={`Bạn đã vượt qua cấp ${currentLevel}`}
          onNextLevel={onNextLevel}
          gameId={ConfigAPI.gameSakuTC}
          competenceId={competenceId}
          totalScore={getBonus()}
        />
        <WinnerModal
          visible={uiState.isWinnerGame}
          onClose={() => {}}
          restartGame={startGame}
          competenceId={competenceId}
          totalScore={60}
          gameId={ConfigAPI.gameSakuTC}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 70,
  },
  birdBubbleContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    transform: [{translateX: 30}, {translateY: -150}],
  },
  birdBubbleRight: {
    position: 'absolute',
    top: 0,
    left: 0,
    transform: [{translateX: -80}, {translateY: -150}],
  },
  bubbleWrapper: {
    width: 100,
    height: 100,
    position: 'relative',
  },
  bubbleText: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    fontSize: 13,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  bubbleTextLeft: {
    transform: [{translateX: -40}, {translateY: -35}],
  },
  bubbleTextRight: {
    transform: [{translateX: -35}, {translateY: -30}],
  },
  birdLeft: {
    transform: [{translateX: -20}],
  },
  birdRight: {
    marginLeft: 70,
  },
  branchContainer: {
    width: '100%',
    height: 100,
    alignItems: 'flex-start',
  },
  branchWrapper: {
    width: '45%',
    position: 'relative',
  },
  branchImage: {
    width: '100%',
    height: 100,
  },
  dropZoneContainer: {
    position: 'absolute',
    bottom: 40,
    left: 0,
    right: 0,
    top: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTextContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  headerTextStyle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  bodyContainer: {
    flex: 1,
    marginTop: 20,
    marginBottom: 30,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    marginHorizontal: 16,
  },
  safeArea: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
  },
  modalContainer: {
    zIndex: 1000,
  },
});

export default StartSakuTC;
