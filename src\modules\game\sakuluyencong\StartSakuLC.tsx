import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Vibration,
} from 'react-native';
import Sound from 'react-native-sound';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import {useEffect, useRef, useState} from 'react';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import {
  PanGestureHandler,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import {runOnJS} from 'react-native-reanimated';
import {BottomGame} from '../components/BottomGame';
import {SafeAreaView} from 'react-native-safe-area-context';
import store, {RootState} from '../../../redux/store/store';
import {useSelector} from 'react-redux';

import GameOverModal from '../components/GameOverModel';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuLCHook} from '../../../redux/hook/game/sakuLCHook';
import {SakuLCWord} from './types/sakuLCTypes';
import ConfigAPI from '../../../Config/ConfigAPI';
import React from 'react';
import {useNavigation, useRoute} from '@react-navigation/native';
import WinnerModal from './components/WinnerModal';
import {GameDA} from '../gameDA';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../../base/baseController';
import GamePauseModal from '../components/ModelPauseGame';
import {checkPositionOrder, hasMaxSort} from '../utils/functions';

// Styles moved up to be accessible by DraggableWord
const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  gameInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  lives: {
    fontSize: 16,
  },
  score: {
    backgroundColor: '#4CAF50',
    color: 'white',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: 'bold',
  },
  instruction: {
    backgroundColor: '#FCF8E8',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropZone: {
    backgroundColor: 'white',
    borderRadius: 10,
    minHeight: 80,
    marginBottom: 20,
    padding: 15,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  dropZoneContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
  },
  dropZoneHint: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 10,
  },
  checkButton: {
    backgroundColor: '#D32F2F',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 30,
  },
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  questionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    flex: 1,
    textAlign: 'center',
  },
  controlButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  controlButton: {
    backgroundColor: '#FF6B35',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: 20,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 20,
  },
  feedbackContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: 'white',
    borderRadius: 8,
    alignItems: 'center',
  },
  feedbackText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
  },
  questionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'nowrap',
    width: '100%',
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  audioIcon: {
    fontSize: 18,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  disabledButtonText: {
    color: '#666666',
  },
});

// DraggableWord component moved outside to prevent re-creation on each render
interface DraggableWordProps {
  word: SakuLCWord;
  dropZoneLayout: any;
  onAddWordToDropZone: (word: SakuLCWord) => void;
  wordsInDropZone: SakuLCWord[];
  isPauseGame: boolean;
}

const DraggableWord = React.memo(
  ({
    word,
    dropZoneLayout,
    onAddWordToDropZone,
    wordsInDropZone,
    isPauseGame,
  }: DraggableWordProps) => {
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);
    const scale = useSharedValue(1);
    const zIndex = useSharedValue(0);

    const gestureHandler = useAnimatedGestureHandler({
      onStart: () => {
        // Không cho phép drag khi game bị pause
        if (isPauseGame) return;

        scale.value = withSpring(1.1);
        zIndex.value = 1000;
      },
      onActive: event => {
        // Không cho phép drag khi game bị pause
        if (isPauseGame) return;

        translateX.value = event.translationX;
        translateY.value = event.translationY;
      },
      onEnd: event => {
        // Không cho phép drag khi game bị pause
        if (isPauseGame) {
          // Reset position nếu game bị pause
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
          scale.value = withSpring(1);
          zIndex.value = 0;
          return;
        }

        const wordY = event.absoluteY;
        const wordX = event.absoluteX;

        const dropZoneX = dropZoneLayout.x;
        const dropZoneY = dropZoneLayout.y;
        const dropZoneWidth = dropZoneX + dropZoneLayout.width + 20;
        const dropZoneHeight = dropZoneY + dropZoneLayout.height + 20;

        // Check if word is dropped in drop zone
        const isInDropZone =
          wordY >= dropZoneY &&
          wordY <= dropZoneHeight &&
          wordX >= dropZoneX &&
          wordX <= dropZoneWidth;

        if (isInDropZone) {
          runOnJS(onAddWordToDropZone)(word);
        }

        // Reset position and scale
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        zIndex.value = 0;
      },
    });

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [
        {translateX: translateX.value},
        {translateY: translateY.value},
        {scale: scale.value},
      ],
      zIndex: zIndex.value,
      opacity: isPauseGame ? 0.5 : 1, // Làm mờ khi pause
    }));

    // Don't render if word is already in drop zone
    if (wordsInDropZone.find(w => w.id === word.id)) {
      return null;
    }

    return (
      <PanGestureHandler onGestureEvent={gestureHandler} enabled={!isPauseGame}>
        <Animated.View style={[styles.wordContainer, animatedStyle]}>
          <Text style={styles.wordText}>{word.text}</Text>
        </Animated.View>
      </PanGestureHandler>
    );
  },
);

const StartSakuLC = () => {
  const {isGameOver, messageGameOver, isRunTime, gem, gemCost} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    availableWords,
    questionDone,
    totalQuestion,
    dropZoneLayout,
    currentQuestion,
    loading,
    error,
    usedHints,
    configLoading,
    configError,
    initialized,
    configInitialized,
    feedbackMessage,
    feedbackType,
    isAnswerCorrect,
    wordsInDropZone: reduxWordsInDropZone,
    // Sử dụng config data từ API
    maxLives,
    currentLives,
    timeLimit,
    timeRemaining,
    gemHint,
  } = useSelector((state: RootState) => state.SakuLC);

  const [wordsInDropZone, setWordsInDropZone] = useState<SakuLCWord[]>([]);
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const sakuLCHook = useSakuLCHook();
  const gameHook = useGameHook();
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  //navigation
  const navigation = useNavigation<any>();
  const refDropZone = useRef<View>(null);
  const hasHint = currentQuestion?.hint && currentQuestion.hint.trim() !== '';
  const isHintUsed = usedHints.includes(currentQuestion?.id?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);
  const [isPauseGame, setIsPauseGame] = useState(false);

  // Handle adding word to drop zone
  const handleAddWordToDropZone = (wordToAdd: SakuLCWord) => {
    setWordsInDropZone(prev => {
      if (!prev.find(w => w.id === wordToAdd.id)) {
        removeWordFromAvailableWords(wordToAdd);
        return [...prev, wordToAdd];
      }
      return prev;
    });
    // xác định lại vị trí drop zone
    findPositionDropZone();
  };

  // Render available words directly
  const renderAvailableWords = () => {
    return availableWords.map((word: SakuLCWord) => (
      <DraggableWord
        key={word.id}
        word={word}
        dropZoneLayout={dropZoneLayout}
        onAddWordToDropZone={handleAddWordToDropZone}
        wordsInDropZone={wordsInDropZone}
        isPauseGame={isPauseGame}
      />
    ));
  };
  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {
    competenceId: '1',
    milestoneId: 1,
  };

  // Load game data on component mount
  useEffect(() => {
    initializeGameData();
    fetchScore();
  }, [competenceId, milestoneId]);
  const fetchScore = async () => {
    try {
      // Lấy thông tin điểm từ bảng GameCUstomer
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        ConfigAPI.gameSakuLC,
      );
      gameHook.setData({stateName: 'gem', value: result ?? 0});
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };
  // Update local state when Redux state changes
  useEffect(() => {
    setWordsInDropZone(reduxWordsInDropZone);
  }, [reduxWordsInDropZone]);

  // Measure drop zone when game is initialized
  useEffect(() => {
    if (initialized && configInitialized && currentQuestion) {
      console.log('===== MEASURING DROP ZONE =====');
      console.log('Current dropZoneLayout:', dropZoneLayout);
      // Add a small delay to ensure the drop zone is rendered
      setTimeout(() => {
        findPositionDropZone();
      }, 100);
    }
  }, [initialized, configInitialized, currentQuestion]);

  useEffect(() => {
    if (currentLives < 1) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Auto next question when answer is correct
  useEffect(() => {
    if (isAnswerCorrect === true) {
      if (questionDone + 1 >= totalQuestion && totalQuestion > 0) {
        setTimeout(() => {
          setShowWinnerModal(true);
          gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
        }, 500); // Delay nhỏ để animation hoàn thành
      }

      setTimeout(() => {
        sakuLCHook.nextQuestion();
        sakuLCHook.clearFeedback();
      }, 1000); // Show success message for 1.5 seconds
    }
  }, [isAnswerCorrect]);

  // Start timer when game is initialized and config is loaded
  useEffect(() => {
    if (initialized && configInitialized) {
      console.log('===== STARTING TIMER =====');
      sakuLCHook.startTimer();
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'time', value: timeRemaining});
      gameHook.setData({stateName: 'gemCost', value: gemHint || 0});
    }
  }, [initialized, configInitialized]);

  // Timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    // Chỉ chạy timer khi game không bị pause và các điều kiện khác đều thỏa mãn
    if (
      initialized &&
      configInitialized &&
      timeRemaining > 0 &&
      !isPauseGame &&
      isRunTime
    ) {
      interval = setInterval(() => {
        sakuLCHook.updateTimer();
        // Sync with global game state for HeadGame component
        gameHook.setData({stateName: 'time', value: timeRemaining - 1});
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [initialized, configInitialized, timeRemaining, isPauseGame, isRunTime]);

  // Game over when time runs out
  useEffect(() => {
    if (timeRemaining <= 0 && initialized) {
      gameOver('Hết giờ rồi, làm lại nào');
    }
  }, [timeRemaining]);

  // Setup audio when question changes
  useEffect(() => {
    if (currentQuestion?.audioUrl) {
      setupAudio(currentQuestion.audioUrl);
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };

  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  const initializeGameData = async () => {
    try {
      console.log('===== STARTING initializeGameData =====');
      console.log('GameId:', ConfigAPI.gameSakuLC);
      console.log('MilestoneId:', milestoneId);
      console.log('CompetenceId:', competenceId);
      // Load game config
      console.log('===== CALLING loadGameConfig =====');
      await sakuLCHook.loadGameConfig(ConfigAPI.gameSakuLC);
      console.log('===== loadGameConfig COMPLETED =====');

      // Load questions
      console.log('===== CALLING loadQuestions =====');
      await sakuLCHook.loadQuestions(
        ConfigAPI.gameSakuLC,
        milestoneId,
        competenceId,
      );
      console.log('===== loadQuestions COMPLETED =====');

      // Initialize game
      console.log('===== CALLING initializeGame =====');
      sakuLCHook.initializeGame();
      console.log('===== initializeGame COMPLETED =====');
    } catch (err) {
      console.error('===== ERROR in initializeGameData:', err);
    }
  };

  const restartGame = () => {
    sakuLCHook.clearFeedback();
    sakuLCHook.reset();
    gameHook.restartGame();

    // Reset pause state khi restart
    setIsPauseGame(false);

    if (configInitialized && timeLimit) {
      console.log(
        `[StartSakuLC] Syncing global timer with SakuLC config: ${timeLimit}s`,
      );
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuLC config - sử dụng sakuLCHook thay vì gameHook
      if (maxLives) {
        console.log(`[StartSakuLC] Resetting lives to maxLives: ${maxLives}`);
        sakuLCHook.setData({stateName: 'maxLives', value: maxLives});
        sakuLCHook.setData({stateName: 'currentLives', value: maxLives});
      }
    } else {
      // Fallback nếu config chưa load
      gameHook.resetGame();
    }
  };

  const togglePauseGame = () => {
    console.log(
      `[StartSakuLC] Toggle pause game: ${isPauseGame ? 'Resume' : 'Pause'}`,
    );

    if (!isPauseGame) {
      // Pause game
      gameHook.setData({stateName: 'isRunTime', value: false});
      setIsPauseGame(true);

      // Pause audio nếu đang phát
      if (isPlayingAudio && audioPlayer) {
        audioPlayer.pause();
        setIsPlayingAudio(false);
      }
    } else {
      // Resume game
      gameHook.setData({stateName: 'isRunTime', value: true});
      setIsPauseGame(false);
    }
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  const onErrorQuestion = () => {
    Vibration.vibrate([0, 500, 200, 500]);

    // Số từ không đúng -> sai -> trừ mạng
    sakuLCHook.setData({
      stateName: 'feedbackMessage',
      value: 'Đáp án sai rồi. hãy thử lại',
    });
    sakuLCHook.setData({
      stateName: 'feedbackType',
      value: 'error',
    });
    sakuLCHook.setData({
      stateName: 'isAnswerCorrect',
      value: false,
    });

    // Trừ mạng
    sakuLCHook.setData({
      stateName: 'currentLives',
      value: currentLives - 1,
    });
  };

  // xác định vị trí drop zone
  const findPositionDropZone = () => {
    console.log('===== findPositionDropZone CALLED =====');
    refDropZone?.current?.measureInWindow((x, y, width, height) => {
      console.log('===== DROP ZONE MEASURED =====');
      console.log('Drop zone coordinates:', {x, y, width, height});
      console.log('Adjusted coordinates:', {x, y: y + 40, width, height});

      sakuLCHook.setData({
        stateName: 'dropZoneLayout',
        value: {x, y: y + 40, width, height},
      });
    });
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    console.log('===== CHECK ANSWER CALLED =====');

    // Kiểm tra xem có đủ số từ không
    if (
      !currentQuestion ||
      wordsInDropZone.length !== currentQuestion.words.length
    )
      return onErrorQuestion();

    const isCorrect = checkPositionOrder(wordsInDropZone);
    if (isCorrect) {
      sakuLCHook.setData({
        stateName: 'feedbackMessage',
        value: 'Đáp án đúng rồi. Chúc mừng bạn',
      });
      sakuLCHook.setData({
        stateName: 'feedbackType',
        value: 'success',
      });
      sakuLCHook.setData({
        stateName: 'isAnswerCorrect',
        value: true,
      });
    } else {
      onErrorQuestion();
    }
  };

  // Xoá text khỏi drop zone
  const removeWordFromDropZone = (word: SakuLCWord) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: [...availableWords, word],
    });
    setWordsInDropZone(prev => prev.filter(w => w.id !== word.id));
    // xác định lại vị trí drop zone
    findPositionDropZone();
  };

  // Xoá text khỏi available words
  const removeWordFromAvailableWords = (word: SakuLCWord) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: availableWords.filter(w => w.id !== word.id),
    });
  };

  // Render text trong drop zone
  const renderWordsInDropZone = () => {
    return wordsInDropZone.map(word => (
      <TouchableOpacity
        key={word.id}
        style={[styles.wordContainer, isPauseGame && {opacity: 0.5}]}
        onPress={isPauseGame ? undefined : () => removeWordFromDropZone(word)}
        disabled={isPauseGame}>
        <Text style={styles.wordText}>{word.text}</Text>
      </TouchableOpacity>
    ));
  };

  const useHint = () => {
    if (gem < (gemHint || 0)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ gem để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gemHint || 0),
    });
    setShowModelConfirm(false);
    if (gemHint > 0) {
      updateScore(gemHint || 0);
    }
    setShowHintModel(true);
    console.log('Hint feature temporarily disabled - no hint data from API');
  };
  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');
    const customerId = store.getState().customer.data.Id;
    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameSakuLC,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gemHint || 0),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - SAKULC_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setShowHintModel(true);
    return true;
  };

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <SafeAreaView style={{flex: 1}}>
        <ImageBackground
          style={{flex: 1}}
          source={require('./assets/background.png')}
          resizeMode="cover">
          <View style={{flex: 1, marginVertical: 16, marginHorizontal: 12}}>
            {/* Header */}
            <HeadGame
              timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
              gameId={ConfigAPI.gameSakuLC}
              isShowSuggest={shouldShowHintButton}
              onUseHint={() => {
                setShowModelConfirm(true);
              }}
            />
            {isPauseGame ? (
              <GamePauseModal
                visible={isPauseGame}
                message={'Bạn đang tạm dừng trò chơi'}
                onContinue={() => {
                  togglePauseGame();
                }}
              />
            ) : (
              <>
                <LineProgressBar
                  progress={
                    (questionDone / totalQuestion) * 100
                  }></LineProgressBar>
                <View
                  style={{
                    width: '100%',
                    marginBottom: 16,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                  <Lives
                    totalLives={maxLives}
                    currentLives={currentLives}></Lives>
                  <CountBadge
                    current={questionDone}
                    total={totalQuestion}></CountBadge>
                </View>

                {/* Loading State */}
                {(loading || configLoading) && (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#112164" />
                    <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
                  </View>
                )}

                {/* Error State */}
                {(error || configError) && (
                  <View style={styles.errorContainer}>
                    <Text style={styles.errorText}>
                      {error || configError || 'Không có câu hỏi cho game này'}
                    </Text>
                  </View>
                )}

                {/* Game Content - Only show when data is loaded */}
                {initialized && configInitialized && currentQuestion && (
                  <>
                    {/* Title question */}
                    <View style={styles.header}>
                      <View style={styles.instruction}>
                        <View style={styles.questionContainer}>
                          {currentQuestion.audioUrl && (
                            <TouchableOpacity
                              style={[
                                styles.audioButton,
                                isPauseGame && {opacity: 0.5},
                              ]}
                              onPress={isPauseGame ? undefined : playAudio}
                              disabled={isPauseGame}>
                              <Text style={styles.audioIcon}>
                                {isPlayingAudio ? '⏸️' : '🔊'}
                              </Text>
                            </TouchableOpacity>
                          )}
                          <Text style={styles.questionText}>
                            {currentQuestion.questionText}
                          </Text>
                        </View>
                      </View>
                    </View>

                    {/* Drop Zone */}
                    <View style={styles.dropZone} ref={refDropZone}>
                      <View style={styles.dropZoneContent}>
                        {renderWordsInDropZone()}
                      </View>
                      {wordsInDropZone.length === 0 && (
                        <Text style={styles.dropZoneHint}>
                          Kéo các từ vào đây
                        </Text>
                      )}

                      {/* Feedback Messages */}
                      {feedbackMessage && (
                        <View style={styles.feedbackContainer}>
                          <Text
                            style={[
                              styles.feedbackText,
                              feedbackType === 'success'
                                ? styles.successText
                                : styles.errorText,
                            ]}>
                            {feedbackMessage}
                          </Text>
                        </View>
                      )}
                    </View>
                  </>
                )}
                {/* Check Answer Button */}
                <TouchableOpacity
                  style={[
                    styles.checkButton,
                    isPauseGame && styles.disabledButton,
                  ]}
                  onPress={isPauseGame ? undefined : checkAnswer}
                  disabled={isPauseGame}>
                  <Text
                    style={[
                      styles.checkButtonText,
                      isPauseGame && styles.disabledButtonText,
                    ]}>
                    {isPauseGame ? 'Game đang tạm dừng' : 'Kiểm tra đáp án'}
                  </Text>
                </TouchableOpacity>

                {/* Available Words - Only show when game is initialized */}
                {initialized && configInitialized && (
                  <View style={styles.wordsContainer}>
                    {renderAvailableWords()}
                  </View>
                )}
              </>
            )}

            {/* Control Buttons */}
            <View style={{position: 'absolute', bottom: 0, left: 0}}>
              <BottomGame
                resetGame={restartGame}
                backGame={() => {
                  navigation.goBack();
                }}
                pauseGame={togglePauseGame}
                volumeGame={() => {}}
              />
            </View>
          </View>
        </ImageBackground>
        <View style={{zIndex: 1000}}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
            message={`Bạn sẽ bị trừ ${gemHint} điểm khi sử dụng trợ giúp này`}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion?.hint ?? ''}
          />
          <GameOverModal
            visible={isGameOver}
            onClose={() => {}}
            restartGame={restartGame}
            message={messageGameOver}
            isTimeOut={false}
          />
          <WinnerModal
            visible={showWinnerModal}
            onClose={() => setShowWinnerModal(false)}
            restartGame={restartGame}
            currentLives={currentLives}
            competenceId={competenceId}
            gameId={ConfigAPI.gameSakuLC}
          />
        </View>
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

export default StartSakuLC;
