#!/bin/bash

echo "🚀 Installing video download dependencies..."

# Install npm packages
echo "📦 Installing npm packages..."
npm install react-native-fs@^2.20.0 react-native-background-downloader@^2.1.1

# For iOS - install pods
echo "🍎 Installing iOS pods..."
cd ios
pod install
cd ..

# For Android - clean and rebuild
echo "🤖 Cleaning Android build..."
cd android
./gradlew clean
cd ..

echo "✅ Installation completed!"
echo ""
echo "📋 Next steps:"
echo "1. For Android: The app should work after rebuilding"
echo "2. For iOS: Make sure to rebuild the app in Xcode"
echo "3. Test the download functionality on both platforms"
echo ""
echo "🔧 Manual steps if needed:"
echo "- Android: Run 'npx react-native run-android'"
echo "- iOS: Run 'npx react-native run-ios' or build in Xcode"
