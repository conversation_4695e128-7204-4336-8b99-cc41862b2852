interface Question {
  id: string;
  level: number;
  title: string;
  listWords: Word[];
  description: string;
  hint: string;
  lastQuestion: boolean;
  sort: number;
}
interface Word {
  id: string;
  sort: number;
  text: string;
}
interface DropZone {
  id: string;
  word: Word | null;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface GameConfig {
  configLv1: Record<string, any>;
  configLv2: Record<string, any>;
  configLv3: Record<string, any>;
}

export type {Word, DropZone, Question, GameConfig};
