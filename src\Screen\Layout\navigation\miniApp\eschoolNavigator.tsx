import {createNativeStackNavigator} from '@react-navigation/native-stack';
import SocialGroups from '../../../../modules/community/groups/listview/groups';
import CourseDetail from '../../../../modules/Course/detail/detail';
import Certificate from '../../../../modules/Course/detail/Certificate';
import LearnCourse from '../../../../modules/Course/learn/learnCourse';
import IntroductionLesson from '../../../../modules/Course/learn/IntroLesson';
import StartALTP from '../../../../modules/game/ailatrieuphu/StartALTP';
import ResultALTP from '../../../../modules/game/ailatrieuphu/ResultALTP';
import RankingScreen from '../../../../modules/game/Ranking/RankingScreen';

import Instructors from '../../../../modules/customer/listview/instructors';
import SettingProfile from '../../../../modules/customer/setting/setting';
import DoingTest from '../../../../modules/exam/views/doingTest';
import OverviewTest from '../../../../modules/exam/views/overviewTest';
import ResultTest from '../../../../modules/exam/views/resultTest';
import HistoryTryingList from '../../../../modules/exam/views/tryingTest/historyTryingList';
import HistoryTryingTest from '../../../../modules/exam/views/tryingTest/historyTryingTest';
import TryingTests from '../../../../modules/exam/views/tryingTest/tryingTests';
import CreateFlashCard from '../../../../modules/flashcard/create';
import {
  DeckDetailScreen,
  FlashcardModeScreen,
} from '../../../../modules/flashcard/learn';
import QuizScreenFlashcard from '../../../../modules/flashcard/Quiz/QuizScreen';
import {navigateReset, RootScreen} from '../../../../router/router';
import ListCoursebyCate from '../../../Page/ListCoursebyCate';
import Order from '../../../Page/Order';
import PurchaseHistory from '../../../Page/PurchaseHistory';
import EschoolLayout from '../../mainLayout';
import {useDispatch} from 'react-redux';
import {useEffect} from 'react';
import LoginScreen from '../../../../modules/customer/login';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {getDataToAsyncStorage} from '../../../../utils/AsyncStorage';
import {SplashScreen} from '../../Splash';
import BiometricSetting from '../../../../modules/customer/setting/biometricSetting';
import PolicyView from '../../../../modules/customer/setting/policy';
import NotificationIndex from '../../../../modules/notification/view';
import VnpayPaymentScreen from '../../../../utils/vnpayWebview';
import ForgotPass from '../../../../modules/customer/form/forgot-pass';
import FAQView from '../../../../modules/customer/listview/FAQView';
import ListCoursebyTopic from '../../../Page/ListCoursebyTopic';
import ProfileCommunity from '../../../../modules/community/pages/profileIndex';
import ProfileRankScreen from '../../../../modules/customer/profileRank';
import OverviewTestNew from '../../../../modules/exam/views/overviewTestNew';
import DoingTestNewRedesigned from '../../../../modules/exam/views/doingTestNewRedesigned';
import ResultTestNew from '../../../../modules/exam/views/resultTestNew';
import StartSakuTB from '../../../../modules/game/sakutimban/StartSakuTB';
import StartSakuLC from '../../../../modules/game/sakuluyencong/StartSakuLC';
import StartMGHH from '../../../../modules/game/manhghephoanhao/StartMGHH';
import StartDHBC from '../../../../modules/game/duoihinhbatchu/StartDHBC';
import GenericGameHomeScreen from '../../../../modules/game/GenericGameHomeScreen';
import SakuXayTo from '../../../../modules/game/sakuxayto/startSakuXayto';
import StartVCNV from '../../../../modules/game/vuotchuongngaivat/StartVCNV';
import StartSakuSM from '../../../../modules/game/sakusanmoi/StartSakuSM';
import StartSakuTC from '../../../../modules/game/sakuchuyencanh/StartSakuTC';
import ProccessPartCourseDetail from '../../../../modules/Course/detail/proccessPartCourseDetail';
import DoingTestNew from '../../../../modules/exam/views/doingTestNew';
import ResultTestQuiz from '../../../../modules/exam/views/resultTestQuiz';
import SavedVideosScreen from '../../../../modules/Course/offline/SavedVideosScreen';
import OfflineVideoPlayer from '../../../../modules/Course/offline/OfflineVideoPlayer';

const Eschool = createNativeStackNavigator();

export function ESchoolStackNavigator() {
  return (
    <Eschool.Navigator
      screenOptions={{headerShown: false, orientation: 'portrait'}}>
      <Eschool.Screen
        name={RootScreen.splashView}
        component={SplashScreenWithAuthCheck}
      />
      <Eschool.Screen
        name={RootScreen.login}
        component={LoginScreen}
        options={{animation: 'fade'}}
      />
      <Eschool.Screen
        name={RootScreen.navigateESchoolView}
        component={EschoolLayout}
      />
      <Eschool.Screen
        name={RootScreen.ProfileRankScreen}
        component={ProfileRankScreen}
      />

      <Eschool.Screen
        name={RootScreen.ProfileCommunity}
        component={ProfileCommunity}
      />

      <Eschool.Screen name={RootScreen.CourseDetail} component={CourseDetail} />
      <Eschool.Screen name={RootScreen.Instructors} component={Instructors} />
      <Eschool.Screen name={RootScreen.Test} component={OverviewTest} />
      <Eschool.Screen
        name={RootScreen.DoingTestNewRedesigned}
        component={DoingTestNewRedesigned}
      />
      <Eschool.Screen
        name={RootScreen.doingTestNew}
        component={DoingTestNew}
      />
      <Eschool.Screen name={RootScreen.doingTest} component={DoingTest} />
      <Eschool.Screen
        name={RootScreen.tryingHistoryTest}
        component={HistoryTryingTest}
      />
      <Eschool.Screen name={RootScreen.tryingTests} component={TryingTests} />
      <Eschool.Screen
        name={RootScreen.historyTryingList}
        component={HistoryTryingList}
      />
      <Eschool.Screen name={RootScreen.resultTest} component={ResultTest} />
      <Eschool.Screen
        name={RootScreen.resultTestNew}
        component={ResultTestNew}
      />
      <Eschool.Screen
        name={RootScreen.resultTestQuiz}
        component={ResultTestQuiz}
      />
      <Eschool.Screen
        name={RootScreen.HomeGame}
        component={GenericGameHomeScreen}
      />
      <Eschool.Screen name={RootScreen.StartALTP} component={StartALTP} />
      <Eschool.Screen name={RootScreen.ResultALTP} component={ResultALTP} />
      <Eschool.Screen name={RootScreen.order} component={Order} />
      <Eschool.Screen name={RootScreen.LearnCourse} component={LearnCourse} />
      <Eschool.Screen
        name={RootScreen.CoursebyTopic}
        component={ListCoursebyTopic}
      />
      <Eschool.Screen
        name={RootScreen.IntroductionLesson}
        component={IntroductionLesson}
      />
      <Eschool.Screen name={RootScreen.Certificate} component={Certificate} />
      <Eschool.Screen
        name={RootScreen.Notification}
        component={NotificationIndex}
      />
      <Eschool.Screen
        name={RootScreen.ProccessCourse}
        component={ProccessPartCourseDetail}
      />
      <Eschool.Screen
        name={RootScreen.listCoursebyCate}
        component={ListCoursebyCate}
      />
      <Eschool.Screen
        name={RootScreen.myCourses}
        component={ListCoursebyCate}
      />
      <Eschool.Screen
        name={RootScreen.SettingProfile}
        component={SettingProfile}
      />
      <Eschool.Screen
        name={RootScreen.BiometricSetting}
        component={BiometricSetting}
      />
      <Eschool.Screen
        name={RootScreen.CreateFlashCard}
        component={CreateFlashCard}
      />

      <Eschool.Screen name={RootScreen.SocialGroups} component={SocialGroups} />
      <Eschool.Screen name={RootScreen.ForgotPass} component={ForgotPass} />
      <Eschool.Screen name={RootScreen.PolicyView} component={PolicyView} />
      <Eschool.Screen name={RootScreen.FAQView} component={FAQView} />
      <Eschool.Screen
        name={RootScreen.VnpayPaymentScreen}
        component={VnpayPaymentScreen}
      />

      <Eschool.Screen
        name={RootScreen.PurchaseHistory}
        component={PurchaseHistory}
      />

      <Eschool.Screen
        name={RootScreen.StartQuizGameFlascard}
        component={QuizScreenFlashcard}
        options={{animation: 'slide_from_bottom'}}
      />
      {/* flashcard */}
      <Eschool.Screen
        name={RootScreen.DeckDetail}
        component={DeckDetailScreen}
      />
      <Eschool.Screen
        name={RootScreen.OverviewTest}
        component={OverviewTestNew}
      />
      <Eschool.Screen
        name={RootScreen.FlashcardMode}
        component={FlashcardModeScreen}
      />
      <Eschool.Screen
        name={RootScreen.GameRanking}
        component={RankingScreen}
        options={{animation: 'slide_from_right'}}
      />

      {/* Game */}
      <Eschool.Screen
        name={RootScreen.StartSakuTB}
        component={StartSakuTB}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartSakuLC}
        component={StartSakuLC}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartSakuSM}
        component={StartSakuSM}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartMGHH}
        component={StartMGHH}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartDHBC}
        component={StartDHBC}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartSakuXT}
        component={SakuXayTo}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartVCNV}
        component={StartVCNV}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name={RootScreen.StartSakuTC}
        component={StartSakuTC}
        options={{animation: 'slide_from_right'}}
      />

      {/* Offline Video Screens */}
      <Eschool.Screen
        name="SavedVideosScreen"
        component={SavedVideosScreen}
        options={{animation: 'slide_from_right'}}
      />
      <Eschool.Screen
        name="OfflineVideoPlayer"
        component={OfflineVideoPlayer}
        options={{animation: 'slide_from_bottom'}}
      />
    </Eschool.Navigator>
  );
}

const SplashScreenWithAuthCheck = ({navigation}: any) => {
  const dispatch = useDispatch<any>();
  useEffect(() => {
    const checkAuthAndNavigate = async () => {
      try {
        // Wait for a minimum of 3 seconds for splash screen
        const splashTimer = new Promise(resolve => setTimeout(resolve, 3000));
        // Check for user token
        const tokenCheck = await getDataToAsyncStorage('accessToken');

        // Wait for both operations to complete
        const [_, accessToken] = await Promise.all([splashTimer, tokenCheck]);
        // Navigate to appropriate screen based on auth status
        // get info
        if (accessToken) {
          dispatch(CustomerActions.getInfor());
        }
        const nextRoute = accessToken
          ? RootScreen.navigateESchoolView
          : RootScreen.login;

        navigateReset(nextRoute);
      } catch (error) {
        console.error('Authentication check error:', error);
        // Default to Auth flow if error occurs
        navigateReset(RootScreen.login);
      }
    };

    checkAuthAndNavigate();
  }, [navigation, dispatch]);

  return <SplashScreen />;
};
